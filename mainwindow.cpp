#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "camerastream.h"
#include <QDebug>
#include <QDateTime>
#include <QTimer>
#include <unistd.h>
#include <uart.c>
#include <ch9350_mouse.h>
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , isRecordEnabled(false)
    , isAudioEnabled(false)
    , isPhotoEnabled(false)
    , currentPhotoState(PHOTO_READY)
    , cameraStream(nullptr)
    , mainGroup(nullptr)
    , recordingIndicatorWidget(nullptr)
    , recordingDotLabel(nullptr)
    , recordingTimeLabel(nullptr)
    , blinkTimer(nullptr)
    , recordingTimer(nullptr)
    , isDotVisible(true)
{
    initializeUI();
    setupButtonGroups();
    setupButtonClickHandlers();
    initializeLabels();
    setupRecordingIndicator();
    initializeCameraStream();
    system("export LIBINPUT_DEBUG_LEVEL=0");
    m_mouseThread = new ch9350_mouse("/dev/ttyS10", this);
    m_mouseThread->start();
}

void MainWindow::initializeUI()
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    showMaximized();

    // 初始状态下隐藏菜单
    ui->stackedWidget->hide();
    ui->widget->hide();
}

void MainWindow::setupButtonGroups()
{
    // 创建主菜单按钮组
    mainGroup = new QButtonGroup(this);
    const QList<QAbstractButton*> mainButtons = {
        ui->camera,
        ui->custom,
        ui->versions,
        ui->language,
        ui->recovery_settings,
        ui->Recording
    };

    // 添加主菜单按钮到组中
    for (int i = 0; i < mainButtons.size(); ++i) {
        mainGroup->addButton(mainButtons[i], i);
    }

    // 初始化子菜单组列表
    for (int i = 0; i < mainButtons.size(); ++i) {
        subGroups.append(nullptr);
    }

    // 设置录像子菜单按钮组
    setupRecordingSubGroup();
}

void MainWindow::setupRecordingSubGroup()
{
    subGroups[0] = new QButtonGroup(this);
    subGroups[0]->addButton(ui->start_recording, 0);
    subGroups[0]->addButton(ui->start_audio, 1);
    subGroups[0]->addButton(ui->start_photo, 2);
}

void MainWindow::initializeLabels()
{
    updateRecordLabel();
    updateAudioLabel();
    updatePhotoLabel();
}

void MainWindow::initializeCameraStream()
{
    cameraStream = new CameraStream(this);
    if (cameraStream) {
        // 循环检测设备，直到找到支持4K60的设备
        bool deviceReady = false;

        while (!deviceReady) 
        {
            // 检测设备是否存在
            if (cameraStream->checkVideoDevice()) {
                qDebug() << "video11设备已找到，检测分辨率支持...";

                // 检测是否支持4K60分辨率
                if (cameraStream->checkResolutionSupport()) {
                    qDebug() << "设备支持4K60分辨率，开始启动摄像头";
                    deviceReady = true;
                }
            } 
            if (!deviceReady) {
                sleep(1); // 等待1秒
            }
        }

        if (deviceReady) {
            // 设备检测通过，启动摄像头
            if (cameraStream->start_camera()) {
                qDebug() << "摄像头启动成功";
                // 连接拍照成功信号
                connect(cameraStream, &CameraStream::photoTaken, this, &MainWindow::onPhotoTaken);

                // 连接摄像头断开和重连信号
                connect(cameraStream, &CameraStream::cameraDisconnected, this, &MainWindow::onCameraDisconnected);
                connect(cameraStream, &CameraStream::cameraReconnected, this, &MainWindow::onCameraReconnected);
                // 等待摄像头初始化完成
                usleep(1000*500); // 500ms
            } else {
                qWarning() << "摄像头启动失败";
            }
        } else {
            qWarning() << "设备检测失败，无法找到支持4K60的video11设备";
        }
    }
}

MainWindow::~MainWindow()
{
    if (cameraStream) {
        delete cameraStream;
    }

    // 清理定时器
    if (blinkTimer) {
        blinkTimer->stop();
        delete blinkTimer;
    }
    if (recordingTimer) {
        recordingTimer->stop();
        delete recordingTimer;
    }
    if (m_mouseThread) {
        m_mouseThread->requestInterruption();
        m_mouseThread->wait();
        delete m_mouseThread;
    }

    delete ui;
}

void MainWindow::keyPressEvent(QKeyEvent *e)
{
    switch (e->key()) {
        case Qt::Key_Q:
            handleQKeyPress();
            break;
        case Qt::Key_Return:
        case Qt::Key_Enter:
            handleEnterKeyPress();
            break;
        case Qt::Key_Left:
        case Qt::Key_Right:
            handleLeftRightKeyPress();
            break;
        case Qt::Key_Up:
        case Qt::Key_Down:
            handleUpDownKeyPress(e->key());
            break;
        default:
            QMainWindow::keyPressEvent(e);
            break;
    }
}

void MainWindow::handleQKeyPress()
{
    if (ui->stackedWidget->currentIndex() != 0) {
        // 从录像页面返回主菜单
        navigateToMainMenu();
    } else {
        // 切换主菜单显示/隐藏
        toggleMainMenuVisibility();
    }
}

void MainWindow::handleEnterKeyPress()
{
    QAbstractButton *current = mainGroup->checkedButton();
    if (current && current == ui->Recording) {
        // 进入录像页面
        ui->stackedWidget->setCurrentIndex(1);
        ui->start_recording->setChecked(true);
    }
}

void MainWindow::handleLeftRightKeyPress()
{
    if (ui->stackedWidget->currentIndex() == 1 && subGroups[0]) {
        QAbstractButton *current = subGroups[0]->checkedButton();
        if (current == ui->start_recording) {
            toggleRecordingState();
        } else if (current == ui->start_audio) {
            toggleAudioState();
        } else if (current == ui->start_photo) {
            togglePhotoState();
        }
    }
}

void MainWindow::handleUpDownKeyPress(int key)
{
    if (ui->stackedWidget->currentIndex() == 1 && subGroups[0]) {
        // 在录像页面处理子菜单导航
        navigateSubMenu(key == Qt::Key_Up);
    } else {
        // 在主菜单处理导航
        navigateMainMenu(key == Qt::Key_Up);
    }
}

void MainWindow::navigateToMainMenu()
{
    ui->stackedWidget->setCurrentIndex(0);
    ui->camera->setChecked(true);
}

void MainWindow::toggleMainMenuVisibility()
{
    ui->stackedWidget->setCurrentIndex(0);
    if (ui->stackedWidget->isHidden()) {
        ui->stackedWidget->show();
        ui->widget->show();
    } else {
        ui->stackedWidget->hide();
        ui->widget->hide();
    }
    ui->camera->setChecked(true);
}

void MainWindow::navigateSubMenu(bool moveUp)
{
    QAbstractButton *current = subGroups[0]->checkedButton();
    if (!current) return;

    int currentId = subGroups[0]->id(current);
    int buttonCount = subGroups[0]->buttons().size();
    int nextId = moveUp ?
        (currentId - 1 + buttonCount) % buttonCount :
        (currentId + 1) % buttonCount;

    subGroups[0]->button(nextId)->setChecked(true);
}

void MainWindow::navigateMainMenu(bool moveUp)
{
    QAbstractButton *current = mainGroup->checkedButton();
    if (!current) return;

    int currentId = mainGroup->id(current);
    int buttonCount = mainGroup->buttons().size();
    int nextId = moveUp ?
        (currentId - 1 + buttonCount) % buttonCount :
        (currentId + 1) % buttonCount;

    mainGroup->button(nextId)->setChecked(true);
}

void MainWindow::toggleRecordingState()
{
    isRecordEnabled = !isRecordEnabled;
    updateRecordLabel();
    handleRecordingStateChange();
}



void MainWindow::toggleAudioState()
{
    if (!validateCameraStream()) {
        return;
    }

    // 检查是否正在录像，如果是则不允许修改音频状态
    if (cameraStream->isRecording) {
        qDebug() << "Cannot change audio state while recording";
        return;
    }

    // 切换音频状态
    curaudiostate = !curaudiostate;

    // 同时更新全局音频状态（为了兼容性）
    isAudioEnabled = curaudiostate;

    updateAudioLabel();
    qDebug() << "Audio state changed to:" << curaudiostate;
}

void MainWindow::togglePhotoState()
{
    if (!validateCameraStream()) {
        return;
    }

    // 只有在PHOTO_READY状态下才能拍照
    if (currentPhotoState != PHOTO_READY) {
        qDebug() << "Cannot take photo, current state:" << currentPhotoState;
        return;
    }

    // 设置为处理中状态
    currentPhotoState = PHOTO_PROCESSING;
    updatePhotoLabel();

    // 拍照功能，直接调用拍照
    cameraStream->takePhoto();
    qDebug() << "Photo request submitted";
}

void MainWindow::updateRecordLabel()
{
    ui->is_record->setText(isRecordEnabled ? "< Y >" : "< N >");
}



void MainWindow::updateAudioLabel()
{
    // 显示音频状态
    ui->is_audio->setText(curaudiostate ? "< Y >" : "< N >");
}
void MainWindow::updatePhotoLabel()
{
    // 根据拍照状态显示不同文本
    switch (currentPhotoState) {
        case PHOTO_READY:
            ui->is_photo->setText("< 是否拍照 >");
            break;
        case PHOTO_SUCCESS:
            ui->is_photo->setText("< 拍照成功 >");
            break;
        case PHOTO_PROCESSING:
            ui->is_photo->setText("< 拍照中... >");
            break;
    }
}


void MainWindow::handleRecordingStateChange()
{
    if (!validateCameraStream()) {
        return;
    }

    if (isRecordEnabled) {
        startRecording();
    } else {
        stopRecording();
    }
}



bool MainWindow::validateCameraStream() const
{
    if (!cameraStream) {
        qWarning() << "CameraStream instance does not exist";
        return false;
    }
    return true;
}

bool MainWindow::isValidChannel(int channel) const
{
    return channel >= 0 && channel < 4;
}

void MainWindow::startRecording()
{
    qDebug() << "Starting recording...";
    cameraStream->startRecording();

    // 显示录像指示器
    showRecordingIndicator();
}

void MainWindow::stopRecording()
{
    qDebug() << "Stopping recording...";
    cameraStream->stopRecording();

    // 隐藏录像指示器
    hideRecordingIndicator();
}

void MainWindow::disableCameraControls()
{
    // 录像时禁用音频控制，防止用户修改音频状态
    qDebug() << "Camera controls disabled during recording";
}

void MainWindow::enableCameraControls()
{
    // 停止录像时重新启用音频控制
    qDebug() << "Camera controls enabled after recording stopped";
}

void MainWindow::setupRecordingIndicator()
{
    // 获取UI中的录像指示器组件
    recordingIndicatorWidget = ui->recordingIndicatorWidget;
    recordingDotLabel = ui->recordingDotLabel;
    recordingTimeLabel = ui->recordingTimeLabel;

    // 初始状态下隐藏录像指示器
    recordingIndicatorWidget->hide();

    // 设置录像指示器的Z-order，确保它在最上层
    recordingIndicatorWidget->raise();

    // 初始定位到右上角
    positionRecordingIndicator();

    // 创建闪烁定时器
    blinkTimer = new QTimer(this);
    connect(blinkTimer, &QTimer::timeout, this, &MainWindow::toggleRecordingDot);

    // 创建录像时间计时器
    recordingTimer = new QTimer(this);
    connect(recordingTimer, &QTimer::timeout, this, &MainWindow::updateRecordingTime);
}

void MainWindow::showRecordingIndicator()
{
    if (!recordingIndicatorWidget) return;

    // 显示录像指示器
    recordingIndicatorWidget->show();

    // 记录录像开始时间
    recordingStartTime = QDateTime::currentDateTime();

    // 重置时间显示
    recordingTimeLabel->setText("00:00:00");

    // 启动闪烁定时器（每500ms闪烁一次）
    isDotVisible = true;
    blinkTimer->start(500);

    // 启动录像时间计时器（每秒更新一次）
    recordingTimer->start(1000);

    qDebug() << "Recording indicator shown";
}

void MainWindow::hideRecordingIndicator()
{
    if (!recordingIndicatorWidget) return;

    // 停止定时器
    if (blinkTimer) {
        blinkTimer->stop();
    }
    if (recordingTimer) {
        recordingTimer->stop();
    }

    // 隐藏录像指示器
    recordingIndicatorWidget->hide();

    qDebug() << "Recording indicator hidden";
}

void MainWindow::updateRecordingTime()
{
    if (!recordingTimeLabel) return;

    // 计算录像时长
    qint64 elapsedSeconds = recordingStartTime.secsTo(QDateTime::currentDateTime());

    // 转换为时:分:秒格式
    int hours = elapsedSeconds / 3600;
    int minutes = (elapsedSeconds % 3600) / 60;
    int seconds = elapsedSeconds % 60;

    QString timeText = QString("%1:%2:%3")
                       .arg(hours, 2, 10, QChar('0'))
                       .arg(minutes, 2, 10, QChar('0'))
                       .arg(seconds, 2, 10, QChar('0'));

    recordingTimeLabel->setText(timeText);
}

void MainWindow::toggleRecordingDot()
{
    if (!recordingDotLabel) return;

    // 切换红点的可见性
    isDotVisible = !isDotVisible;
    recordingDotLabel->setVisible(isDotVisible);
}

void MainWindow::positionRecordingIndicator()
{
    if (!recordingIndicatorWidget) return;

    // 获取主窗口的大小
    QSize windowSize = this->size();

    // 计算右上角位置
    int x = windowSize.width() - recordingIndicatorWidget->width() - 20;  // 距离右边20像素
    int y = 20;  // 距离顶部20像素

    // 设置位置
    recordingIndicatorWidget->move(x, y);

    qDebug() << "Recording indicator positioned at:" << x << y;
}

void MainWindow::onPhotoTaken()
{
    // 拍照成功，设置为成功状态
    currentPhotoState = PHOTO_SUCCESS;
    updatePhotoLabel();
    qDebug() << "Photo taken successfully";

    // 延迟2秒后恢复到准备状态
    QTimer::singleShot(500, this, [this]() {
        currentPhotoState = PHOTO_READY;
        updatePhotoLabel();
        qDebug() << "Photo state reset to ready";
    });
}

void MainWindow::onCameraDisconnected()
{
    qWarning() << "摄像头已断开连接";

    // 如果正在录像，强制停止
    if (cameraStream && cameraStream->isRecording) {
        qWarning() << "摄像头断开，强制停止录像";
        isRecordEnabled = false;
        updateRecordLabel();
        hideRecordingIndicator();
    }

    // 可以在这里添加UI提示，告知用户摄像头已断开
    qDebug() << "等待摄像头重新连接...";
}

void MainWindow::onCameraReconnected()
{
    qDebug() << "摄像头已重新连接";

    // 可以在这里添加UI提示，告知用户摄像头已重连
    qDebug() << "摄像头重连成功，可以继续使用";
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 窗口大小改变时重新定位录像指示器
    positionRecordingIndicator();
}

// 设置按钮点击处理器
void MainWindow::setupButtonClickHandlers()
{
    // 连接主菜单按钮的点击信号
    connect(mainGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, &MainWindow::handleMainButtonClick);

    // 连接录像子菜单按钮的点击信号
    if (subGroups[0]) {
        connect(subGroups[0], QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
                this, &MainWindow::handleSubButtonClick);
    }
}

// 主菜单按钮点击处理
void MainWindow::handleMainButtonClick(QAbstractButton* button)
{
    if (!button) return;

    // 如果菜单隐藏，先显示菜单
    if (ui->stackedWidget->isHidden()) {
        ui->stackedWidget->show();
        ui->widget->show();
    }

    // 如果点击的按钮未选中，则选中它
    if (!button->isChecked()) {
        button->setChecked(true);
        return;
    }

    // 如果点击的是已选中的按钮，执行相应功能
    if (button == ui->Recording) {
        // 进入录像页面
        ui->stackedWidget->setCurrentIndex(1);
        ui->start_recording->setChecked(true);
    }
    // 其他按钮可以在这里添加相应的功能
}

// 子菜单按钮点击处理
void MainWindow::handleSubButtonClick(QAbstractButton* button)
{
    if (!button) return;

    // 如果点击的按钮未选中，则选中它
    if (!button->isChecked()) {
        button->setChecked(true);
        return;
    }

    // 如果点击的是已选中的按钮，执行相应功能
    if (button == ui->start_recording) {
        toggleRecordingState();
    } else if (button == ui->start_audio) {
        toggleAudioState();
    } else if (button == ui->start_photo) {
        togglePhotoState();
    }
}
